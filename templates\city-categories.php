<?php
/* ==================== city-categories.php ==================== */
$selected_city = glowess_get_selected_city();
if ($selected_city):
    $categories = glowess_get_city_categories($selected_city);
    if (!empty($categories)):
?>
<section class="glowess-city-categories" id="categories">
    <div class="container">
        <h2 class="section-title">Kategoriler</h2>
        <div class="glowess-city-category-slider">
            <?php foreach ($categories as $category): 
                $category_icon = get_term_meta($category->term_id, 'category_icon', true);
                $product_count = $category->count;
            ?>
                <div class="glowess-city-category-item" 
                     data-category-id="<?php echo $category->term_id; ?>"
                     data-scroll-target="#category-<?php echo $category->term_id; ?>">
                    <div class="glowess-city-category-icon">
                        <?php if ($category_icon): ?>
                            <img src="<?php echo esc_url($category_icon); ?>" 
                                 alt="<?php echo esc_attr($category->name); ?>"
                                 loading="lazy">
                        <?php else: ?>
                            🌸
                        <?php endif; ?>
                    </div>
                    <div class="glowess-city-category-name"><?php echo esc_html($category->name); ?></div>
                    <div class="glowess-city-category-count"><?php echo $product_count; ?> ürün</div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; endif; ?>