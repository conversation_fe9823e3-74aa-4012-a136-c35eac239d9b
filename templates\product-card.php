<?php
/* ==================== product-card.php ==================== */
// Bu template tek ürün kartı için kullanılacak
global $product;
if (!$product) return;

$product_id = $product->get_id();
$product_image = wp_get_attachment_image_src(get_post_thumbnail_id($product_id), 'woocommerce_thumbnail');
$product_gallery = $product->get_gallery_image_ids();
$second_image = !empty($product_gallery) ? wp_get_attachment_image_src($product_gallery[0], 'woocommerce_thumbnail') : null;
$regular_price = $product->get_regular_price();
$sale_price = $product->get_sale_price();
$discount_percentage = $regular_price && $sale_price ? round((($regular_price - $sale_price) / $regular_price) * 100) : 0;
$rating = $product->get_average_rating();
$review_count = $product->get_review_count();
$is_in_stock = $product->is_in_stock();
?>

<div class="glowess-city-product-card <?php echo !$is_in_stock ? 'out-of-stock' : ''; ?>">
    <div class="glowess-city-product-image">
        <a href="<?php echo get_permalink($product_id); ?>">
            <?php if ($product_image): ?>
                <img src="<?php echo esc_url($product_image[0]); ?>" 
                     alt="<?php echo esc_attr($product->get_name()); ?>"
                     <?php if ($second_image): ?>
                     data-hover-src="<?php echo esc_url($second_image[0]); ?>"
                     <?php endif; ?>
                     loading="lazy"
                     class="primary-image">
            <?php else: ?>
                <div class="no-image-placeholder">
                    <span>Görsel Yok</span>
                </div>
            <?php endif; ?>
        </a>
        
        <?php if ($discount_percentage > 0): ?>
            <div class="glowess-city-discount-badge">
                -%<?php echo $discount_percentage; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!$is_in_stock): ?>
            <div class="stock-badge out-of-stock">
                Stokta Yok
            </div>
        <?php elseif ($product->get_stock_quantity() <= 5 && $product->get_stock_quantity() > 0): ?>
            <div class="stock-badge low-stock">
                Son <?php echo $product->get_stock_quantity(); ?> Adet
            </div>
        <?php endif; ?>
        
        <div class="product-actions">
            <button class="glowess-city-wishlist" 
                    data-product-id="<?php echo $product_id; ?>"
                    title="Favorilere Ekle">
                <span class="heart-icon">♡</span>
            </button>
            <button class="glowess-city-quick-view" 
                    data-product-id="<?php echo $product_id; ?>"
                    title="Hızlı İncele">
                <span class="eye-icon">👁</span>
            </button>
        </div>
    </div>
    
    <div class="glowess-city-product-info">
        <div class="product-category">
            <?php
            $categories = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'names'));
            if (!empty($categories)) {
                echo esc_html($categories[0]);
            }
            ?>
        </div>
        
        <h3 class="glowess-city-product-title">
            <a href="<?php echo get_permalink($product_id); ?>">
                <?php echo esc_html($product->get_name()); ?>
            </a>
        </h3>
        
        <div class="product-short-description">
            <?php echo wp_trim_words($product->get_short_description(), 15); ?>
        </div>
        
        <?php if ($rating > 0): ?>
            <div class="glowess-city-product-rating">
                <div class="stars" title="<?php echo esc_attr($rating); ?> / 5 yıldız">
                    <?php
                    for ($i = 1; $i <= 5; $i++) {
                        if ($i <= floor($rating)) {
                            echo '<span class="star filled">★</span>';
                        } elseif ($i <= ceil($rating)) {
                            echo '<span class="star half">★</span>';
                        } else {
                            echo '<span class="star empty">☆</span>';
                        }
                    }
                    ?>
                </div>
                <span class="rating-text"><?php echo number_format($rating, 1); ?> (<?php echo $review_count; ?>)</span>
            </div>
        <?php endif; ?>
        
        <div class="glowess-city-product-price">
            <?php if ($sale_price): ?>
                <span class="glowess-city-old-price"><?php echo wc_price($regular_price); ?></span>
                <span class="glowess-city-new-price"><?php echo wc_price($sale_price); ?></span>
                <span class="savings">
                    <?php echo wc_price($regular_price - $sale_price); ?> tasarruf!
                </span>
            <?php else: ?>
                <span class="glowess-city-new-price"><?php echo $product->get_price_html(); ?></span>
            <?php endif; ?>
        </div>
        
        <?php if ($is_in_stock): ?>
            <button class="glowess-city-add-to-cart" 
                    data-product-id="<?php echo $product_id; ?>"
                    data-quantity="1">
                <span class="button-text">Sepete Ekle</span>
                <span class="button-loader" style="display: none;">
                    <span class="spinner"></span>
                </span>
            </button>
        <?php else: ?>
            <button class="glowess-city-out-of-stock-button" disabled>
                Stokta Yok
            </button>
        <?php endif; ?>
        
        <div class="product-meta">
            <?php if ($product->get_sku()): ?>
                <span class="sku">SKU: <?php echo esc_html($product->get_sku()); ?></span>
            <?php endif; ?>
            
            <?php if ($product->get_weight()): ?>
                <span class="weight">Ağırlık: <?php echo esc_html($product->get_weight()); ?>kg</span>
            <?php endif; ?>
        </div>
    </div>
</div>