<?php
/**
 * TEMPLATE DOSYALARI - templates/ klasörüne kaydedilecek
 */

/* ==================== city-selector-modal.php ==================== */
?>
<div id="city-selector-modal" class="city-modal-overlay" style="display: none;">
    <div class="city-modal">
        <div class="city-modal-header">
            <h2>Hangi şehirdesiniz?</h2>
            <p>Size en uygun ürünleri gösterebilmek için şehrinizi seçin</p>
        </div>
        <div class="city-grid">
            <?php
            $cities = get_posts(array(
                'post_type' => 'cities',
                'numberposts' => -1,
                'post_status' => 'publish',
                'meta_query' => array(
                    array(
                        'key' => '_city_is_active',
                        'value' => '1',
                        'compare' => '='
                    )
                )
            ));
            
            foreach ($cities as $city):
                $city_image = get_post_meta($city->ID, '_city_hero_image', true);
                $city_slug = get_post_meta($city->ID, '_city_slug', true);
            ?>
                <div class="city-option glowess-fade-in" 
                     data-city-id="<?php echo $city->ID; ?>" 
                     data-city-slug="<?php echo esc_attr($city_slug); ?>">
                    <?php if ($city_image): ?>
                        <img src="<?php echo esc_url($city_image); ?>" 
                             alt="<?php echo esc_attr($city->post_title); ?>"
                             loading="lazy">
                    <?php else: ?>
                        <div class="city-placeholder-icon">🏙️</div>
                    <?php endif; ?>
                    <span><?php echo esc_html($city->post_title); ?></span>
                </div>
            <?php endforeach; ?>
        </div>
        <?php if (empty($cities)): ?>
            <div class="city-empty-state">
                <p>Henüz aktif şehir bulunmamaktadır.</p>
            </div>
        <?php endif; ?>
    </div>
</div>