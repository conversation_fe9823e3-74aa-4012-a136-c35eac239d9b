<?php
/* ==================== cart-threshold-notice.php ==================== */
$cart_threshold = get_option('glowess_city_cart_threshold', 500);
$discount_percentage = get_option('glowess_city_discount_percentage', 10);
$info_text = get_option('glowess_city_info_text', 'X ₺ üzeri alışverişlerde sepetinize otomatik %Y indirim uygulanır.');

// Replace placeholders
$info_text = str_replace('X', $cart_threshold, $info_text);
$info_text = str_replace('Y', $discount_percentage, $info_text);

$cart = WC()->cart;
$cart_total = $cart ? $cart->get_subtotal() : 0;
$remaining = $cart_threshold - $cart_total;
?>

<div class="glowess-city-cart-threshold">
    <?php if ($remaining > 0): ?>
        <div class="threshold-remaining">
            <span class="threshold-icon">🎯</span>
            <span class="threshold-text">
                Sepetinize <strong><?php echo wc_price($remaining); ?></strong> daha ekleyin ve 
                <strong class="discount-highlight">%<?php echo $discount_percentage; ?></strong> indirim kazanın!
            </span>
            <div class="threshold-progress">
                <div class="progress-bar" style="width: <?php echo min(100, ($cart_total / $cart_threshold) * 100); ?>%"></div>
            </div>
        </div>
    <?php else: ?>
        <div class="threshold-achieved">
            <span class="achievement-icon">🎉</span>
            <span class="achievement-text">
                <strong>Tebrikler!</strong> Sepetinize <strong class="discount-highlight">%<?php echo $discount_percentage; ?></strong> indirim uygulandı!
            </span>
        </div>
    <?php endif; ?>
</div>