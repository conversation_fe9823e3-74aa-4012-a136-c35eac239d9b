# Glowess City Based E-Commerce Plugin

WordPress 6.8.2, WooCommerce ve Glowess teması ile uyumlu şehir bazlı e-ticaret eklentisi.

## 📋 Özellikler

- **Şehir Bazlı Ürün Yönetimi**: Ürünleri şehirlere göre kategorize edin
- **Dinamik İçerik**: Seçilen şehre göre ana sayfa, kategoriler ve ürünler
- **Modern UI/UX**: Beymen tarzı keskin köşeli tasarım
- **Mobil Uyumlu**: Tüm cihazlarda mükemmel görünüm
- **SEO Dostu**: Arama motorları için optimize edilmiş
- **WooCommerce Entegrasyonu**: Tam uyumlu sepet ve ödeme sistemi
- **Admin Panel**: <PERSON><PERSON> yönetim arayüzü

## 🚀 Kurulum

### 1. <PERSON><PERSON><PERSON>
```
glowess-city-ecommerce/
├── glowess-city-ecommerce.php (Ana plugin dosyası)
├── assets/
│   ├── style.css
│   ├── script.js
│   └── admin.css
├── templates/
│   ├── city-selector-modal.php
│   ├── city-hero.php
│   ├── city-categories.php
│   ├── city-products.php
│   ├── delivery-info.php
│   ├── city-selector-dropdown.php
│   ├── product-card.php
│   ├── cart-threshold-notice.php
│   ├── cross-sell-products.php
│   ├── checkout-city-fields.php
│   └── order-received-whatsapp.php
├── languages/
└── README.md
```

### 2. WordPress'e Yükleme

1. **ZIP olarak yükle:**
   - Tüm dosyaları `glowess-city-ecommerce` klasörüne koyun
   - Klasörü ZIP olarak sıkıştırın
   - WordPress admin → Eklentiler → Yeni Ekle → Eklenti Yükle

2. **FTP ile yükle:**
   - Tüm dosyaları `/wp-content/plugins/glowess-city-ecommerce/` dizinine yükleyin
   - WordPress admin'den eklentiyi etkinleştirin

### 3. Gereksinimler Kontrolü

- ✅ WordPress 6.0+
- ✅ WooCommerce 5.0+
- ✅ PHP 7.4+
- ✅ Glowess teması (önerilen)

## ⚙️ İlk Kurulum

### 1. Şehir Ekleme
```
WordPress Admin → Şehirler → Yeni Şehir Ekle

Gerekli Bilgiler:
- Şehir Adı: İstanbul
- Şehir Kodu: istanbul
- Hero Görseli: Şehir için büyük görsel URL'si
- Teslimat Alanları: Her satıra bir ilçe
- Teslimat Süreleri: İlçe: Süre formatında
- Durum: Aktif/Pasif
```

### 2. Ürünlere Şehir Atama
```
WooCommerce → Ürünler → Ürün Düzenle
"Bu ürün hangi şehirlerde satılacak?" bölümünden şehirleri seçin
```

### 3. Plugin Ayarları
```
WordPress Admin → Şehir Ayarları
- Sepet Eşik Tutarı: 500₺
- İndirim Yüzdesi: 10%
- Bilgi Metni: Sepet sayfasında gösterilecek metin
```

## 🎨 Tema Entegrasyonu

### Glowess Teması İçin

**Ana sayfa template'i (index.php):**
```php
<?php
get_header();

// Şehir seçim event'i
jQuery(document).on('city_selected', function(e, cityId, cityName) {
    console.log('Şehir seçildi:', cityName);
    // Özel kodunuz
});

// Sepete ekleme event'i
jQuery(document).on('product_added_to_cart', function(e, productId) {
    console.log('Ürün sepete eklendi:', productId);
    // Analytics tracking vb.
});
```

### PHP Hooks
```php
// Şehir seçildikten sonra
add_action('glowess_city_selected', function($city_id, $city_slug) {
    // Özel işlemler
}, 10, 2);

// Ürün kartı render edilirken
add_filter('glowess_city_product_card_html', function($html, $product) {
    // HTML'yi değiştir
    return $html;
}, 10, 2);
```

## 📊 Analytics ve Tracking

### Google Analytics 4
```javascript
// Otomatik olarak tracking yapılır
gtag('event', 'city_selected', {
    'city_name': 'Istanbul',
    'city_id': 1
});

gtag('event', 'add_to_cart', {
    'product_id': 123,
    'product_name': 'Gül Buketi'
});
```

### Facebook Pixel
```javascript
// Otomatik tracking
fbq('track', 'CitySelected', {
    city_name: 'Istanbul'
});

fbq('track', 'AddToCart', {
    product_id: 123
});
```

## 🐛 Sorun Giderme

### Yaygın Sorunlar

**1. Şehir modalı görünmüyor**
```php
// functions.php'ye ekle
add_action('wp_footer', function() {
    if (!glowess_get_selected_city()) {
        echo '<script>console.log("City modal should show");</script>';
    }
});
```

**2. Ürünler gösterilmiyor**
```php
// Debug için
$products = glowess_get_city_products($city_id);
var_dump($products->found_posts); // Kaç ürün bulundu?
```

**3. CSS çalışmıyor**
```php
// Cache temizle
wp_enqueue_script('glowess-city-js', plugin_dir_url(__FILE__) . 'assets/script.js', array('jquery'), time());
```

**4. AJAX çalışmıyor**
```javascript
// Console'da kontrol et
console.log(glowess_city_ajax); // AJAX config'i görmeli
```

### Debug Modu
```
URL sonuna #debug ekle: yoursite.com#debug
Console'da: window.glowessCityDebug
```

## 🔒 Güvenlik

### NONCE Kontrolü
Plugin tüm AJAX istekleri için nonce kontrolü yapar:
```php
wp_verify_nonce($_POST['nonce'], 'glowess_city_nonce')
```

### SQL Injection Koruması
```php
// Tüm DB sorguları prepared statements ile
$wpdb->prepare("SELECT * FROM posts WHERE ID = %d", $city_id);
```

### XSS Koruması
```php
// Tüm output'lar escape edilir
echo esc_html($city_name);
echo esc_url($image_url);
echo esc_attr($city_slug);
```

## 🚀 Performans Optimizasyonu

### Caching
```php
// Şehir verilerini cache'le
$cities = wp_cache_get('glowess_active_cities');
if (false === $cities) {
    $cities = get_posts(/* query */);
    wp_cache_set('glowess_active_cities', $cities, '', 3600);
}
```

### Lazy Loading
```html
<!-- Görseller lazy load ile -->
<img src="placeholder.jpg" data-src="real-image.jpg" class="lazy" loading="lazy">
```

### Database Optimizasyonu
```sql
-- Performans için index'ler
ALTER TABLE wp_postmeta ADD INDEX glowess_city_idx (meta_key, meta_value(10));
```

## 📱 PWA Hazırlığı

Plugin PWA özelliklerine hazır:
- Service Worker desteği
- Offline cache stratejisi
- App-like deneyim

## 🌐 Çoklu Dil Desteği

### WPML Uyumluluğu
```php
// Şehir adlarını çevir
$city_name = apply_filters('wpml_translate_single_string', $city_name, 'glowess-city', $city_name);
```

### Polylang Uyumluluğu
```php
// Dil değişiminde şehir seçimini koru
add_filter('pll_the_languages_link', function($link, $lang) {
    $selected_city = glowess_get_selected_city();
    if ($selected_city) {
        $link = add_query_arg('city', $selected_city, $link);
    }
    return $link;
}, 10, 2);
```

## 🔄 Güncelleme

### Otomatik Güncelleme
```php
// functions.php - Plugin güncellemelerini kontrol et
add_filter('pre_set_site_transient_update_plugins', 'check_glowess_city_update');
```

### Manuel Güncelleme
1. Yeni sürümü indirin
2. Eski plugin dosyalarını silin
3. Yeni dosyaları yükleyin
4. Plugin'i yeniden etkinleştirin

## 📈 A/B Testing

### Conversion Tracking
```php
// Conversion rate'i ölç
add_action('woocommerce_thankyou', function($order_id) {
    $selected_city = glowess_get_selected_city();
    update_post_meta($order_id, '_order_city', $selected_city);
});
```

### Heatmap Entegrasyonu
```javascript
// Hotjar, Crazy Egg vb. için
window.glowessCityTracking = {
    selectedCity: getCookie('selected_city_slug'),
    version: '1.0.0'
};
```

## 🎯 Marketing Entegrasyonu

### Email Marketing
```php
// MailChimp, Klaviyo vb. için şehir bilgisi
add_filter('newsletter_subscriber_data', function($data) {
    $data['city'] = glowess_get_selected_city_name();
    return $data;
});
```

### CRM Entegrasyonu
```php
// HubSpot, Salesforce vb. için
add_action('woocommerce_checkout_order_processed', function($order_id) {
    $order = wc_get_order($order_id);
    $selected_city = glowess_get_selected_city();
    
    // CRM'e gönder
    send_to_crm(array(
        'order_id' => $order_id,
        'city' => $selected_city,
        'customer_data' => $order->get_billing_email()
    ));
});
```

## 🎨 UI/UX Optimizasyonu

### Loading States
```css
.glowess-city-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    animation: spin 1s linear infinite;
}
```

### Micro-interactions
```css
.city-option {
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.city-option:hover {
    transform: translateY(-5px) scale(1.02);
}
```

## 📊 Reporting

### Admin Dashboard Widget
```php
add_action('wp_dashboard_setup', function() {
    wp_add_dashboard_widget(
        'glowess_city_stats',
        'Şehir İstatistikleri',
        'render_city_stats_widget'
    );
});
```

### Custom Reports
```php
// En çok satan şehir
function get_top_selling_cities() {
    global $wpdb;
    
    return $wpdb->get_results("
        SELECT city_id, COUNT(*) as orders
        FROM {$wpdb->postmeta} pm
        WHERE pm.meta_key = '_order_city'
        GROUP BY city_id
        ORDER BY orders DESC
        LIMIT 10
    ");
}
```

## 🛡️ Backup ve Restore

### Settings Export
```php
// Ayarları dışa aktar
function export_glowess_city_settings() {
    $settings = array(
        'cities' => get_posts(array('post_type' => 'cities')),
        'options' => array(
            'cart_threshold' => get_option('glowess_city_cart_threshold'),
            'discount_percentage' => get_option('glowess_city_discount_percentage')
        )
    );
    
    return json_encode($settings);
}
```

## 📞 Destek

### Loglama
```php
// Hata logları
if (WP_DEBUG) {
    error_log('Glowess City Debug: ' . print_r($debug_data, true));
}
```

### System Info
```php
function glowess_city_system_info() {
    return array(
        'wp_version' => get_bloginfo('version'),
        'wc_version' => WC()->version,
        'theme' => get_template(),
        'php_version' => PHP_VERSION,
        'plugin_version' => GLOWESS_CITY_VERSION
    );
}
```

## 🚀 Gelişmiş Özellikler

### Multi-vendor Desteği
```php
// Dokan, WC Vendors vb. için
add_filter('glowess_city_vendor_products', function($products, $city_id, $vendor_id) {
    // Vendor'a özel şehir filtreleme
    return $products;
}, 10, 3);
```

### Subscription Desteği
```php
// WooCommerce Subscriptions için
add_action('woocommerce_subscription_renewal_payment_complete', function($subscription) {
    $city = get_post_meta($subscription->get_id(), '_subscription_city', true);
    // Şehir bazlı yenileme işlemleri
});
```

---

## 📄 Lisans

Bu plugin GPL v2 veya üstü lisans ile lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📧 İletişim

- **Geliştirici**: [Your Name]
- **Email**: [<EMAIL>]
- **Website**: [https://yourwebsite.com]

---

**Not**: Bu plugin Glowess teması ile optimize edilmiştir ancak diğer temalarla da uyumlu çalışır.imi kontrolü
$selected_city = glowess_get_selected_city();
if (!$selected_city) {
    // Şehir seçim modalını göster
    include plugin_dir_path(__DIR__) . 'glowess-city-ecommerce/templates/city-selector-modal.php';
    get_footer();
    return;
}

// Hero bölümü
include plugin_dir_path(__DIR__) . 'glowess-city-ecommerce/templates/city-hero.php';

// Kategoriler
include plugin_dir_path(__DIR__) . 'glowess-city-ecommerce/templates/city-categories.php';

// Ürünler
include plugin_dir_path(__DIR__) . 'glowess-city-ecommerce/templates/city-products.php';

// Teslimat bilgileri
include plugin_dir_path(__DIR__) . 'glowess-city-ecommerce/templates/delivery-info.php';

get_footer();
?>
```

**Header'a şehir seçici eklemek:**
```php
// header.php içinde
<div class="header-city">
    <?php 
    if (function_exists('glowess_get_selected_city')) {
        include plugin_dir_path(__DIR__) . 'glowess-city-ecommerce/templates/city-selector-dropdown.php';
    }
    ?>
</div>
```

### Shortcode Kullanımı

```php
// Şehir seçici dropdown
[city_selector style="dropdown"]

// Şehre özel ürünler
[city_products limit="12" category="cicekler" columns="4"]
```

## 📱 Mobil Entegrasyonu

Plugin otomatik olarak mobil uyumlu tasarıma sahiptir:

- **Responsive Grid**: Mobilde 1 kolon, tablette 2, web'de 3-4 kolon
- **Touch Slider**: Kategori slider'ı touch desteği
- **Modal Optimization**: Mobil için optimize edilmiş şehir seçimi
- **Sticky Elements**: Mobilde yapışkan sepet butonu

## 🛒 WooCommerce Entegrasyonu

### Sepet Sayfası Özelleştirme
```php
// theme/woocommerce/cart/cart.php başına ekle
if (function_exists('glowess_get_selected_city')) {
    include plugin_dir_path(__DIR__) . '../plugins/glowess-city-ecommerce/templates/cart-threshold-notice.php';
    include plugin_dir_path(__DIR__) . '../plugins/glowess-city-ecommerce/templates/cross-sell-products.php';
}
```

### Checkout Sayfası
```php
// functions.php'ye ekle
add_action('woocommerce_checkout_after_customer_details', function() {
    if (function_exists('glowess_get_selected_city')) {
        include plugin_dir_path(__DIR__) . 'plugins/glowess-city-ecommerce/templates/checkout-city-fields.php';
    }
});
```

## 🔧 Özelleştirme

### CSS Özelleştirme
```css
/* Tema colors.css veya style.css */

/* Şehir modal renkleri */
.city-modal-overlay {
    background: rgba(0, 0, 0, 0.9); /* Daha koyu overlay */
}

.city-option:hover {
    border-color: #your-brand-color;
}

/* Ürün kartı renkleri */
.glowess-city-discount-badge {
    background: #your-brand-color;
}
```

### JavaScript Hooks
```javascript
// Şehir seç