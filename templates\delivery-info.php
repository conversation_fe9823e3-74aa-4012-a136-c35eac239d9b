<?php
/* ==================== delivery-info.php ==================== */
$selected_city = glowess_get_selected_city();
if ($selected_city):
    $city = get_post($selected_city);
    $delivery_areas = get_post_meta($selected_city, '_city_delivery_areas', true);
    $delivery_times = get_post_meta($selected_city, '_city_delivery_times', true);
    $city_name = $city->post_title;
?>
<section class="glowess-city-delivery-info" id="delivery-info">
    <div class="container">
        <button class="glowess-city-delivery-toggle" 
                data-closed-text="<?php echo esc_attr($city_name); ?> Teslimat Detayları"
                data-open-text="Teslimat Detaylarını Gizle">
            <?php echo esc_html($city_name); ?> Teslimat Detayları
        </button>
        
        <div class="glowess-city-delivery-content">
            <?php if ($delivery_areas): ?>
                <h3>Teslimat Bölgeleri</h3>
                <div class="glowess-city-delivery-areas">
                    <?php 
                    $areas = explode("\n", $delivery_areas);
                    $times = explode("\n", $delivery_times);
                    $time_map = array();
                    
                    foreach ($times as $time_line) {
                        if (strpos($time_line, ':') !== false) {
                            $parts = explode(':', $time_line, 2);
                            $time_map[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                    
                    foreach ($areas as $area):
                        $area = trim($area);
                        if (!empty($area)):
                            $delivery_time = isset($time_map[$area]) ? $time_map[$area] : '30-60 dakika';
                    ?>
                        <div class="glowess-city-delivery-area">
                            <strong><?php echo esc_html($area); ?></strong>
                            <span><?php echo esc_html($delivery_time); ?></span>
                        </div>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </div>
            <?php endif; ?>
            
            <div class="delivery-additional-info">
                <h3>Teslimat Bilgileri</h3>
                <ul>
                    <li>Siparişler 24 saat içinde hazırlanır</li>
                    <li>Ücretsiz kargo seçenekleri mevcuttur</li>
                    <li>Aynı gün teslimat imkanı</li>
                    <li>Güvenli paketleme garantisi</li>
                </ul>
            </div>
            
            <div class="delivery-contact">
                <p>Teslimat ile ilgili sorularınız için:</p>
                <a href="tel:+905001234567" class="contact-phone">📞 0500 123 45 67</a>
                <a href="https://wa.me/905001234567" class="contact-whatsapp">💬 WhatsApp</a>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>
